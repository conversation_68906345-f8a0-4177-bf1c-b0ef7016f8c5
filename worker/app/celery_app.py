import os

from celery import Celery

broker_url = os.getenv("REDIS_URL", "redis://localhost:6379/0")
backend_url = os.getenv("REDIS_URL", "redis://localhost:6379/0")

celery_app = Celery("wp", broker=broker_url, backend=backend_url)
celery_app.conf.task_routes = {"app.tasks.*": {"queue": "default"}}
celery_app.autodiscover_tasks(["app.tasks"])

celery_app.conf.beat_schedule = {
    "heartbeat-every-1m": {
        "task": "app.tasks.heartbeat.ping",
        "schedule": 60.0,
        "options": {"queue": "default"},
    },
}
