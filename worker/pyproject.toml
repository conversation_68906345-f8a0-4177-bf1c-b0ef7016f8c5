[project]
name = "worship-platform-worker"
version = "0.1.0"
description = "Background jobs (transpose, media, emails, reports)"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
  "celery>=5.4",
  "redis>=5.0",
  "psycopg2-binary>=2.9",
  "boto3>=1.34",         # or "minio>=7.2"
  "pydantic>=2.7",
  "requests>=2.32",
]

[project.optional-dependencies]
dev = [
  "pytest>=8.0",
  "ruff>=0.5",
  "mypy>=1.10",
]
