project-includes = ["api/app", "worker/app", "tests"]
project-excludes = ["**/__pycache__", "**/.venv/**", "build", "dist", "api/migrations"]

python-version = "3.12"
python-platform = "linux"

[errors]
# Strict core
missing-return-annotation = true
missing-parameter-annotation = true
incompatible-assign = true
incompatible-return = true
incompatible-call = true
invalid-override = true

# Migration noise — on but non-fatal in CI (use exit code gating if needed)
untyped-function = true
untyped-class = true
implicit-optional = true

# Framework dynamics
unknown-attribute = false
