[project]
name = "worship-platform"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = []


[tool.uv.workspace]
members = ["api", "worker"]

[project.optional-dependencies]
dev = [
  "ruff>=0.5",
  # CLI for Pyrefly (type checker + language server)
  "pyrefly>=0.2",
]

[tool.ruff]
target-version = "py312"
line-length = 100
extend-exclude = ["migrations", "build", "dist"]
lint.select = ["E", "F", "I", "UP", "B", "SIM"]
lint.ignore = [
  "E203", # allow black-style slices if you use Black later
]
# Enable import sorting
[tool.ruff.lint.isort]
known-first-party = ["app", "src"]

# --- ty configuration lives in [tool.ty] (see below) ---
[tool.ty]
python-version = "3.12"

[tool.ty.rules]
# Examples: tune as you adopt typing across the codebase
# "index-out-of-bounds" = "warning"
# "missing-parameter-annotation" = "ignore"

# pyrefly.toml
project-includes = ["app", "src", "tests"]
project-excludes = ["**/__pycache__", "**/.venv/**", "build", "dist", "migrations"]

python-version = "3.12"
python-platform = "linux"

[errors]
# Turn specific error kinds on/off as you migrate
# bad-assignment = true
# bad-return = true
