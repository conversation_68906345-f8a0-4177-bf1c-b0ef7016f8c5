[rules]
missing-return-type = "error"
missing-parameter-type = "error"
invalid-override = "error"
incompatible-assignment = "error"
incompatible-call-argument = "error"
incompatible-return = "error"
unused-ignore = "warning"
untyped-function = "warning"
untyped-class = "warning"
implicit-optional = "warning"
dynamic-attribute = "ignore"
unknown-member = "warning"


python-version = "3.12"

[rules]
# Baseline expectations
missing-return-type = "error"
missing-parameter-type = "error"
invalid-override = "error"
incompatible-assignment = "error"
incompatible-call-argument = "error"
incompatible-return = "error"
unused-ignore = "warning"

# Helpful but noisy while migrating — start at "warning"
untyped-function = "warning"
untyped-class = "warning"
implicit-optional = "warning"

# Framework-aware relaxations (see per-path overrides below)
# Many frameworks attach attributes dynamically; keep these off globally.
dynamic-attribute = "ignore"
unknown-member = "warning"

# Optional, if you want to ratchet later
redundant-cast = "warning"
unreachable-code = "warning"

# ---------- Per-path overrides ----------
# FastAPI routers often use decorators/build response models dynamically.
# Loosen strict function param annotations to ease onboarding.
[[overrides]]
paths = ["api/app/routers/**", "api/app/main.py"]
[rules]
missing-parameter-type = "warning"
untyped-function = "warning"
unknown-member = "warning"

# Pydantic models infer types from fields; allow model_config/dynamic validators.
[[overrides]]
paths = ["api/app/schemas/**"]
[rules]
untyped-class = "warning"
missing-return-type = "warning"
unknown-member = "ignore"   # Pydantic attaches __get_validators__, etc.

# SQLAlchemy ORM models: mapped attributes & relationships can appear dynamic.
[[overrides]]
paths = ["api/app/db/**"]
[rules]
unknown-member = "ignore"
dynamic-attribute = "ignore"
incompatible-assignment = "warning"

# Alembic migrations are imperative & dynamic — don't block CI.
[[overrides]]
paths = ["api/migrations/**"]
[rules]
missing-parameter-type = "ignore"
missing-return-type = "ignore"
untyped-function = "ignore"
unknown-member = "ignore"

# Celery tasks — allow flexible signatures while migrating.
[[overrides]]
paths = ["worker/app/tasks/**"]
[rules]
missing-parameter-type = "warning"
missing-return-type = "warning"
untyped-function = "warning"


[[overrides]]
paths = ["api/app/routers/**", "api/app/main.py"]
[rules]
missing-parameter-type = "warning"
untyped-function = "warning"
unknown-member = "warning"

[[overrides]]
paths = ["api/app/schemas/**"]
[rules]
untyped-class = "warning"
missing-return-type = "warning"
unknown-member = "ignore"

[[overrides]]
paths = ["api/app/db/**"]
[rules]
unknown-member = "ignore"
dynamic-attribute = "ignore"
incompatible-assignment = "warning"

[[overrides]]
paths = ["api/app/migrations/**"]
[rules]
missing-parameter-type = "ignore"
missing-return-type = "ignore"
untyped-function = "ignore"
unknown-member = "ignore"

[[overrides]]
paths = ["worker/app/tasks/**"]
[rules]
missing-parameter-type = "warning"
missing-return-type = "warning"
untyped-function = "warning"
