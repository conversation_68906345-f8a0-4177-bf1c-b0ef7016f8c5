server {
    listen                  80;

    client_max_body_size    50M;

    location / {
        proxy_pass          http://web:3000;
        proxy_set_header    Host $host;
    }

    location /api/ {
        rewrite             ^/api/?(.*)$ /$1 break;
        proxy_pass          http://api:8000;
        proxy_set_header    Host $host;
    }

    location /realtime/ {
        proxy_http_version  1.1;
        proxy_set_header    Upgrade $http_upgrade;
        proxy_set_header    Connection "Upgrade";
        rewrite             ^/realtime/?(.*)$ /$1 break;
        proxy_pass          http://realtime:8080;
    }
}
