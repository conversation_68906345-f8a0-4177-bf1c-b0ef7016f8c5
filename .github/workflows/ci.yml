name: CI

on:
  push: { branches: ['main'] }
  pull_request:

jobs:
  lint-types:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Install uv
        run: curl -LsSf https://astral.sh/uv/install.sh | sh -s -- -y

      - name: Sync deps (dev)
        run: |
          ~/.local/bin/uv lock
          ~/.local/bin/uv sync --all-extras

      - name: Ruff (lint + format check)
        run: ~/.local/bin/uv run ruff check .

      - name: <PERSON> (type check)
        run: ~/.local/bin/uvx ty check .

      - name: <PERSON>yrefly (type check)
        run: ~/.local/bin/uv run pyrefly check
      - name: Ty (type check - non-blocking)
        run: |
          set +e
          ~/.local/bin/uvx ty check . | tee ty.out
          # Fail only on 'error', allow 'warning' during migration
          ! grep -E "\b(error)\b" ty.out
