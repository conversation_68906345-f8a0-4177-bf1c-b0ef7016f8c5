repos:
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.13.1
    hooks:
      - id: ruff
        args: ['--fix']
      - id: ruff-format

  # Ty doesn't ship a pre-commit hook yet; use a local hook
  - repo: local
    hooks:
      - id: ty-check
        name: ty check
        entry: uvx ty check .
        language: system
        pass_filenames: false

      - id: pyrefly-check
        name: pyrefly check
        entry: uv run pyrefly check
        language: system
        pass_filenames: false
