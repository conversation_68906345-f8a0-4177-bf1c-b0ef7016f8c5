version: "3.9"

name: worship-platform

services:
  db:
    image: postgres:16-alpine
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
    ports:
      - "${POSTGRES_PORT}:5432"
    volumes:
      - db_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 5s
      timeout: 3s
      retries: 20

  redis:
    image: redis:7-alpine
    command: ["redis-server", "--save", "", "--appendonly", "no"]
    ports:
      - "${REDIS_PORT}:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 20

  minio:
    image: minio/minio:latest
    command: server /data --console-address ":${MINIO_CONSOLE_PORT}"
    environment:
      MINIO_ROOT_USER: ${MINIO_ACCESS_KEY}
      MINIO_ROOT_PASSWORD: ${MINIO_SECRET_KEY}
    ports:
      - "${MINIO_PORT}:9000"
      - "${MINIO_CONSOLE_PORT}:9001"
    volumes:
      - minio_data:/data
    healthcheck:
      test: ["CMD", "mc", "ready", "local"]
      interval: 10s
      timeout: 5s
      retries: 10

  mailhog:
    image: mailhog/mailhog:v1.0.1
    ports:
      - "${MAILHOG_SMTP_PORT}:1025"
      - "${MAILHOG_HTTP_PORT}:8025"

  api:
    build:
      context: ./api
      dockerfile: Dockerfile
    env_file: .env
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_started
    environment:
      DATABASE_URL: "postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@db:5432/${POSTGRES_DB}"
      REDIS_URL: "redis://redis:6379/0"
      STORAGE_ENDPOINT: "http://minio:9000"
      STORAGE_BUCKET: "${MINIO_BUCKET}"
      STORAGE_ACCESS_KEY: "${MINIO_ACCESS_KEY}"
      STORAGE_SECRET_KEY: "${MINIO_SECRET_KEY}"
      EMAIL_HOST: "mailhog"
      EMAIL_PORT: "1025"
    expose:
      - "8000"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/healthz"]
      interval: 5s
      timeout: 3s
      retries: 20

  realtime:
    build:
      context: ./realtime
      dockerfile: Dockerfile
    env_file: .env
    depends_on:
      redis:
        condition: service_healthy
    environment:
      REDIS_URL: "redis://redis:6379/0"
      PORT: ${REALTIME_PORT}
    expose:
      - "8080"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/healthz"]
      interval: 5s
      timeout: 3s
      retries: 20

  worker:
    build:
      context: ./worker
      dockerfile: Dockerfile
    env_file: .env
    depends_on:
      api:
        condition: service_healthy
      redis:
        condition: service_healthy
      db:
        condition: service_healthy
    environment:
      DATABASE_URL: "postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@db:5432/${POSTGRES_DB}"
      REDIS_URL: "redis://redis:6379/0"
      STORAGE_ENDPOINT: "http://minio:9000"
      STORAGE_BUCKET: "${MINIO_BUCKET}"
      STORAGE_ACCESS_KEY: "${MINIO_ACCESS_KEY}"
      STORAGE_SECRET_KEY: "${MINIO_SECRET_KEY}"

  web:
    build:
      context: ./web
      dockerfile: Dockerfile
    env_file: .env
    environment:
      NEXT_PUBLIC_API_BASE_URL: "http://localhost/api"
      NEXT_PUBLIC_REALTIME_URL: "ws://localhost/realtime"
    depends_on:
      api:
        condition: service_healthy
      realtime:
        condition: service_healthy
    expose:
      - "3000"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 10s
      timeout: 5s
      retries: 20

  nginx:
    image: nginx:1.27-alpine
    depends_on:
      web:
        condition: service_healthy
      api:
        condition: service_healthy
      realtime:
        condition: service_healthy
    volumes:
      - ./nginx/default.conf:/etc/nginx/conf.d/default.conf:ro
    ports:
      - "80:80"

volumes:
  db_data:
  minio_data:
