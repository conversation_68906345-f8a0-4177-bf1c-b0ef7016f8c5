## Type checking & linting

- **Install deps**: `uv lock && uv sync --all-extras`
- **Ruff (lint)**: `uv run ruff check .`
- **<PERSON> (fast checker)**: `uvx ty check .`
- **Pyrefly (checker + IDE)**:
  - CLI: `uv run pyrefly check`
  - VS Code: install the “Pyrefly” extension and open the workspace.

> <PERSON> reads `[tool.ty]` from `pyproject.toml` or `ty.toml`. Pyrefly reads `pyrefly.toml` or `[tool.pyrefly]` in `pyproject.toml`. See docs for details. :contentReference[oaicite:7]{index=7}
