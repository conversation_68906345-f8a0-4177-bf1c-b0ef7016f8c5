[project]
name = "worship-platform-api"
version = "0.1.0"
description = "API for worship leader & musician support platform"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
  "fastapi>=0.115",
  "uvicorn[standard]>=0.30",
  "pydantic>=2.7",
  "psycopg2-binary>=2.9",
  "sqlalchemy>=2.0",
  "alembic>=1.13",
  "redis>=5.0",
  "boto3>=1.34",           # or "minio>=7.2" if using MinIO client lib
  "python-multipart>=0.0.9",
  "httpx>=0.27",
]

[project.optional-dependencies]
dev = [
  "pytest>=8.0",
  "pytest-asyncio>=0.23",
  "ruff>=0.5",
  "mypy>=1.10",
  "types-redis",
  "types-requests",
]

[tool.uv]
# Lock file is uv.lock (generated by `uv lock`)

[tool.ruff]
target-version = "py312"
line-length = 100
lint.select = ["E", "F", "I", "UP", "B", "SIM", "ANN"]  # ANN = typing-related hints
lint.ignore = [
  "ANN101",  # self missing type annotation (noise for methods)
  "ANN102",  # cls missing type annotation
]

[tool.ruff.lint.isort]
known-first-party = ["app"]
