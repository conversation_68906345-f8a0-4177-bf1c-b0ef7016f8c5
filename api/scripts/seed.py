# api/scripts/seed.py
import os
import uuid

from sqlalchemy import create_engine
from sqlalchemy.orm import Session

from app.db.models import ServiceItem, ServicePlan, Song, User

DATABASE_URL = os.getenv("DATABASE_URL")
engine = create_engine(DATABASE_URL, future=True, pool_pre_ping=True)


def guid() -> str:
    return uuid.uuid4().hex


def run():
    with Session(engine) as s:
        wl = User(id=guid(), name="Worship Leader", email="<EMAIL>")
        s.add(wl)

        sng = Song(
            id=guid(),
            title="Amazing Grace",
            default_key="G",
            tempo=72,
            time_signature="3/4",
            tags=["hymn", "grace"],
        )
        s.add(sng)

        plan = ServicePlan(id=guid(), title="Sunday Worship")
        s.add(plan)
        s.flush()

        item = ServiceItem(
            id=guid(),
            plan_id=plan.id,
            type="song",
            position=1,
            expected_duration="00:05:00",
            song_id=sng.id,
        )
        s.add(item)
        s.commit()
        print("Seeded: user, song, plan, item")


if __name__ == "__main__":
    run()
