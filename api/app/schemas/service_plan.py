# api/app/schemas/service_plan.py
from datetime import datetime, timedelta
from typing import Literal

from pydantic import BaseModel, Field

ItemType = Literal["song", "scripture", "prayer", "sermon"]


class ServiceItem(BaseModel):
    id: str
    type: ItemType
    position: int
    expected_duration: timedelta | None = None
    song_id: str | None = None
    notes: dict[str, str] = Field(default_factory=dict)


class ServicePlan(BaseModel):
    id: str
    title: str
    date: datetime
    items: list[ServiceItem] = Field(default_factory=list)

    model_config = {
        "from_attributes": True,  # ORM mode replacement in Pydantic v2
    }
