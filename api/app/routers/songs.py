# api/app/routers/songs.py
from typing import Annotated

from fastapi import APIRouter, Depends
from pydantic import BaseModel, Field

router = APIRouter(prefix="/songs", tags=["songs"])


class SongIn(BaseModel):
    title: str = Field(..., min_length=1)
    key: str = Field(..., min_length=1)
    tempo: int | None = None


class SongOut(SongIn):
    id: str


def get_repo() -> "SongRepo":  # during bootstrap you can refine "SongRepo"
    from app.services.songs import SongRepo

    return SongRepo()


RepoDep = Annotated["SongRepo", Depends(get_repo)]  # clear dependency typing


@router.post("/", response_model=SongOut)
def create_song(payload: SongIn, repo: RepoDep) -> SongOut:
    return repo.create(payload)
