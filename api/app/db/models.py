# api/app/db/models.py
from __future__ import annotations

from typing import List, Optional

from sqlalchemy import (
    JSON,
    TIMESTAMP,
    Foreign<PERSON>ey,
    Integer,
    Interval,
    String,
    Text,
    text,
)
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.db.base import Base


class User(Base):
    __tablename__ = "users"
    id: Mapped[str] = mapped_column(String, primary_key=True)
    name: Mapped[str] = mapped_column(String, nullable=False)
    email: Mapped[str] = mapped_column(String, unique=True, nullable=False)
    preferences: Mapped[dict] = mapped_column(JSON, default=dict)


class Song(Base):
    __tablename__ = "songs"
    id: Mapped[str] = mapped_column(String, primary_key=True)
    title: Mapped[str] = mapped_column(String, nullable=False)
    ccli_id: Mapped[Optional[str]] = mapped_column(String)
    default_key: Mapped[Optional[str]]
    tempo: Mapped[Optional[int]]
    time_signature: Mapped[Optional[str]]
    tags: Mapped[Optional[List[str]]] = mapped_column(JSON)
    last_played: Mapped[Optional[str]] = mapped_column(TIMESTAMP(timezone=True))


class ServicePlan(Base):
    __tablename__ = "service_plans"
    id: Mapped[str] = mapped_column(String, primary_key=True)
    title: Mapped[str] = mapped_column(String, nullable=False)
    date: Mapped[Optional[str]] = mapped_column(
        TIMESTAMP(timezone=True), server_default=text("now()")
    )
    notes: Mapped[dict] = mapped_column(JSON, default=dict)
    items: Mapped[List["ServiceItem"]] = relationship(
        back_populates="plan", cascade="all, delete-orphan"
    )


class ServiceItem(Base):
    __tablename__ = "service_items"
    id: Mapped[str] = mapped_column(String, primary_key=True)
    plan_id: Mapped[str] = mapped_column(ForeignKey("service_plans.id"))
    type: Mapped[str] = mapped_column(String, nullable=False)  # song/scripture/...
    position: Mapped[int] = mapped_column(Integer, nullable=False)
    expected_duration: Mapped[Optional[str]] = mapped_column(Interval)
    meta: Mapped[dict] = mapped_column(JSON, default=dict)
    song_id: Mapped[Optional[str]] = mapped_column(ForeignKey("songs.id"))

    plan: Mapped["ServicePlan"] = relationship(back_populates="items")
    song: Mapped[Optional["Song"]] = relationship()


class Assignment(Base):
    __tablename__ = "assignments"
    id: Mapped[str] = mapped_column(String, primary_key=True)
    plan_id: Mapped[str] = mapped_column(ForeignKey("service_plans.id"))
    user_id: Mapped[str] = mapped_column(ForeignKey("users.id"))
    role: Mapped[str] = mapped_column(String, nullable=False)  # WL, GTR, KEYS, ...
    status: Mapped[str] = mapped_column(
        String, default="invited"
    )  # invited/accepted/declined


class MediaFile(Base):
    __tablename__ = "media_files"
    id: Mapped[str] = mapped_column(String, primary_key=True)
    song_id: Mapped[Optional[str]] = mapped_column(ForeignKey("songs.id"))
    uploader_id: Mapped[Optional[str]] = mapped_column(ForeignKey("users.id"))
    type: Mapped[str] = mapped_column(String)  # chart/pdf/audio/stem/video
    path: Mapped[str] = mapped_column(Text, nullable=False)  # s3://bucket/key
    variants: Mapped[dict] = mapped_column(JSON, default=dict)
